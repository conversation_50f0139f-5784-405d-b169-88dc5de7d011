import React, { useState } from 'react';
import { 
  LayoutDashboard, 
  Users, 
  MessageSquare, 
  Bot, 
  Shield, 
  Settings,
  Plug,
  ChevronDown,
  ChevronRight,
  Bell,
  Search,
  User,
  Menu,
  X
} from 'lucide-react';

const EnterpriseAIAdmin = () => {
  const [activeMenuItem, setActiveMenuItem] = useState('dashboard');
  const [expandedMenus, setExpandedMenus] = useState({});
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const menuItems = [
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: LayoutDashboard,
      component: DashboardContent
    },
    {
      id: 'accounts',
      title: 'Accounts',
      icon: Users,
      subItems: [
        {
          id: 'account-management',
          title: 'Account Management',
          subOptions: ['All Users', 'Create New User', 'User Roles & Permissions', 'Group Memberships', 'Identity Provider Sync']
        },
        {
          id: 'personalization-settings',
          title: 'Personalization Settings',
          subOptions: ['View/Edit Settings', 'Role-Based Settings', 'Settings Inheritance', 'Settings Versioning']
        },
        {
          id: 'preferences',
          title: 'Preferences',
          subOptions: ['General Preferences', 'AI Interaction Preferences', 'Workflow Preferences']
        },
        {
          id: 'memory-management',
          title: 'Memory Management',
          subOptions: ['All Memories', 'Create New Memory', 'Memory Categories', 'Semantic Search', 'Memory Access Logs']
        }
      ]
    },
    {
      id: 'chat-history',
      title: 'Chat History',
      icon: MessageSquare,
      subOptions: ['Chat Sessions', 'Messages', 'Memory References', 'Engagement Metrics']
    },
    {
      id: 'assistants-agents',
      title: 'Assistants & Agents',
      icon: Bot,
      subOptions: ['Assistant Access Control', 'Agent Access Control', 'Usage Quotas', 'Access Logs']
    },
    {
      id: 'security',
      title: 'Security',
      icon: Shield,
      subOptions: ['JWT Token Management', 'Identity Providers', 'Role-Based Access Control', 'Audit Logs', 'Data Encryption Settings']
    },
    {
      id: 'integration-center',
      title: 'Integration Center',
      icon: Plug,
      subOptions: ['Frontend Integration', 'AI System Integration', 'External System Sync', 'Monitoring & Alerts']
    },
    {
      id: 'configuration',
      title: 'Configuration',
      icon: Settings,
      subOptions: ['Environment Variables', 'Authentication Settings', 'Database Configuration', 'Application Settings']
    }
  ];

  const toggleMenu = (menuId) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuId]: !prev[menuId]
    }));
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-64' : 'w-16'} transition-all duration-300 bg-white shadow-lg border-r border-gray-200 flex flex-col`}>
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {sidebarOpen && (
              <h1 className="text-xl font-bold text-gray-800">
                <span className="text-blue-600">Enterprise</span>AI
              </h1>
            )}
            <button
              onClick={toggleSidebar}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              {sidebarOpen ? <X size={20} /> : <Menu size={20} />}
            </button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto py-4">
          {menuItems.map((item) => (
            <div key={item.id} className="mb-1">
              <button
                onClick={() => {
                  if (item.subItems || item.subOptions) {
                    toggleMenu(item.id);
                  }
                  setActiveMenuItem(item.id);
                }}
                className={`w-full flex items-center px-4 py-3 text-left hover:bg-gray-50 transition-colors ${
                  activeMenuItem === item.id ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                }`}
              >
                <item.icon size={20} className={`${activeMenuItem === item.id ? 'text-blue-600' : 'text-gray-500'}`} />
                {sidebarOpen && (
                  <>
                    <span className={`ml-3 font-medium ${activeMenuItem === item.id ? 'text-blue-600' : 'text-gray-700'}`}>
                      {item.title}
                    </span>
                    {(item.subItems || item.subOptions) && (
                      <div className="ml-auto">
                        {expandedMenus[item.id] ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                      </div>
                    )}
                  </>
                )}
              </button>

              {/* Sub Items */}
              {sidebarOpen && expandedMenus[item.id] && (item.subItems || item.subOptions) && (
                <div className="ml-8 mt-1">
                  {item.subItems ? (
                    item.subItems.map((subItem) => (
                      <div key={subItem.id} className="mb-2">
                        <button
                          onClick={() => toggleMenu(subItem.id)}
                          className="w-full flex items-center px-3 py-2 text-left hover:bg-gray-50 transition-colors rounded-lg"
                        >
                          <span className="text-sm font-medium text-gray-600">{subItem.title}</span>
                          <div className="ml-auto">
                            {expandedMenus[subItem.id] ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
                          </div>
                        </button>
                        {expandedMenus[subItem.id] && (
                          <div className="ml-4 mt-1">
                            {subItem.subOptions.map((option, index) => (
                              <button
                                key={index}
                                className="w-full text-left px-3 py-1.5 text-xs text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded"
                              >
                                {option}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    item.subOptions.map((option, index) => (
                      <button
                        key={index}
                        className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg"
                      >
                        {option}
                      </button>
                    ))
                  )}
                </div>
              )}
            </div>
          ))}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold text-gray-800 capitalize">
              {activeMenuItem.replace('-', ' ')}
            </h2>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <button className="p-2 rounded-lg hover:bg-gray-100 relative">
                <Bell size={20} className="text-gray-600" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
              </button>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <User size={16} className="text-white" />
                </div>
                <span className="font-medium text-gray-700">Admin</span>
              </div>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <main className="flex-1 overflow-y-auto p-6">
          {activeMenuItem === 'dashboard' && <DashboardContent />}
          {activeMenuItem !== 'dashboard' && <DefaultContent title={activeMenuItem} />}
        </main>
      </div>
    </div>
  );
};

// Dashboard Content Component
function DashboardContent() {
  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users size={24} className="text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">2,847</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <MessageSquare size={24} className="text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Chat Sessions</p>
              <p className="text-2xl font-bold text-gray-900">15,239</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Bot size={24} className="text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Agents</p>
              <p className="text-2xl font-bold text-gray-900">127</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Shield size={24} className="text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Security Events</p>
              <p className="text-2xl font-bold text-gray-900">3</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">System Usage Analytics</h3>
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <p className="text-gray-500">Usage Analytics Chart</p>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Activity</h3>
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((item) => (
              <div key={item} className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium text-gray-800">User action performed</p>
                  <p className="text-xs text-gray-500">2 minutes ago</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Notifications */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Notifications</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div>
              <p className="font-medium text-blue-800">System Update Available</p>
              <p className="text-sm text-blue-600">New features and security improvements</p>
            </div>
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              Update
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Default Content Component
function DefaultContent({ title }) {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
      <div className="text-center">
        <h3 className="text-2xl font-semibold text-gray-800 mb-4 capitalize">
          {title.replace('-', ' ')} Module
        </h3>
        <p className="text-gray-600 mb-6">
          This section is under development. The {title.replace('-', ' ')} functionality will be available soon.
        </p>
        <div className="bg-gray-50 rounded-lg p-6">
          <p className="text-sm text-gray-500">
            Content for {title} will be implemented here with all the specified sub-options and features.
          </p>
        </div>
      </div>
    </div>
  );
}

// This is the default export that React will use
export default EnterpriseAIAdmin;